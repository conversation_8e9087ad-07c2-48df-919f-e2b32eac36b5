//
//  iCloudSyncStatusView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/3.
//

import SwiftUI

/**
 * iCloud同步状态显示组件
 * 显示同步状态、最后同步时间和手动同步按钮
 */
struct iCloudSyncStatusView: View {
    
    @ObservedObject var syncManager: iCloudSyncManager
    
    var body: some View {
        VStack(spacing: 12) {
            // 同步状态行
            HStack {
                // 状态图标
                Image(systemName: syncManager.syncStatus.iconName)
                    .foregroundColor(statusColor)
                    .font(.system(size: 16, weight: .medium))
                
                // 状态文本
                Text(syncManager.syncStatus.displayText)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(statusColor)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
                
                Spacer()
                
                // 手动同步按钮
                if syncManager.isSyncEnabled && syncManager.isAvailable {
                    But<PERSON>(action: {
                        Task {
                            await syncManager.triggerManualSync()
                        }
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 12, weight: .medium))
                            Text("手动同步")
                                .font(.system(size: 12, weight: .medium))
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)
                        }
                        .foregroundColor(.blue)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.blue.opacity(0.1))
                        )
                    }
                    .disabled(syncManager.syncStatus == .syncing || syncManager.syncStatus == .migrating)
                }
            }
            
            // 最后同步时间
            if let lastSyncDate = syncManager.lastSyncDate {
                HStack {
                    Text("最后同步:")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                        .lineLimit(1)

                    Text(formatSyncDate(lastSyncDate))
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                        .minimumScaleFactor(0.8)

                    Spacer()
                }
            }
            
            // 错误信息
            if let errorMessage = syncManager.errorMessage {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                        .font(.system(size: 12))
                    
                    Text(errorMessage)
                        .font(.system(size: 12))
                        .foregroundColor(.orange)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Computed Properties
    
    private var statusColor: Color {
        switch syncManager.syncStatus {
        case .idle:
            return .secondary
        case .syncing, .migrating:
            return .blue
        case .success:
            return .green
        case .failed:
            return .red
        }
    }
    
    // MARK: - Helper Methods
    
    private func formatSyncDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        let now = Date()
        let calendar = Calendar.current

        // 检查是否是今天
        if calendar.isDate(date, inSameDayAs: now) {
            formatter.dateFormat = "HH:mm"
            return "今天 \(formatter.string(from: date))"
        }

        // 检查是否是昨天
        if let yesterday = calendar.date(byAdding: .day, value: -1, to: now),
           calendar.isDate(date, inSameDayAs: yesterday) {
            formatter.dateFormat = "HH:mm"
            return "昨天 \(formatter.string(from: date))"
        }

        // 检查是否在本周
        if calendar.dateInterval(of: .weekOfYear, for: now)?.contains(date) == true {
            formatter.dateFormat = "EEEE HH:mm"
            return formatter.string(from: date)
        } else {
            formatter.dateFormat = "MM-dd HH:mm"
            return formatter.string(from: date)
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        // 同步成功状态
        iCloudSyncStatusView(syncManager: {
            let manager = iCloudSyncManager.shared
            manager.syncStatus = .success
            manager.lastSyncDate = Date()
            manager.isSyncEnabled = true
            manager.isAvailable = true
            return manager
        }())
        
        // 同步中状态
        iCloudSyncStatusView(syncManager: {
            let manager = iCloudSyncManager.shared
            manager.syncStatus = .syncing
            manager.isSyncEnabled = true
            manager.isAvailable = true
            return manager
        }())
        
        // 错误状态
        iCloudSyncStatusView(syncManager: {
            let manager = iCloudSyncManager.shared
            manager.syncStatus = .failed
            manager.errorMessage = "网络连接失败"
            manager.isSyncEnabled = true
            manager.isAvailable = false
            return manager
        }())
    }
    .padding()
}
